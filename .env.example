# Application Port
PORT=5000
# AWS SDK
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_COMPRESSED_ITEMS_BUCKET=
AWS_COMPRESSED_ITEMS_REGION=
AWS_S3_ASSETS_BUCKET=
AWS_S3_ASSETS_REGION=
AWS_S3_VESSEL_THUMBNAILS_FOLDER=

CLOUDFRONT_URL=
CLOUDFRONT_KEY_PAIR_ID=
CLOUDFRONT_PRIVATE_KEY=

OPENAI_API_KEY=
# Mongo DB
MONGO_URI=
# JWT
JWT_SECRET=
# NodeMailer Settings for Google Account
API_URL='http://localhost:5000/api'
APP_URL='http://localhost:3000'
MAIL_USER=
CLIENT_ID=
CLIENT_SECRET=
REDIRECT_URI=
REFRESH_TOKEN=
# Others
NODE_ENV='dev'
PROCESS_LOG_INTERVAL=60000
DB_QUERY_CONCURRENCY=5
GOOGLE_API_KEY=

REDIS_PORT=6379
REDIS_HOST=127.0.0.1
ARTIFACT_THUMBNAILS_CACHE_LIFETIME=86400
ARTIFACT_THUMBNAILS_CACHE_VIDEO=true