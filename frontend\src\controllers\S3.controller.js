import environment from "../../environment.js";
import axiosInstance from "../axios.js";
import idb from "../indexedDB.js";

class S3Controller {
    constructor() {
        this.cacheStore = "s3_url_cache";
    }

    async getCachedUrl(cacheKey) {
        try {
            const cachedItemArr = await idb.getItems(this.cacheStore, (item) => item._id === cacheKey);
            const cachedItem = cachedItemArr[0];
            if (!cachedItem) {
                return null; // No cached item found
            }

            const now = Date.now();

            // Check if item is expired
            if (cachedItem.expiresAt && now >= cachedItem.expiresAt) {
                // Remove expired item
                await idb.deleteItem(this.cacheStore, cacheKey);
                return null;
            }

            // Item exists and is not expired
            return cachedItem.url;
        } catch (error) {
            console.error('[S3Controller] Error getting cached URL:', error);
            return null;
        }
    }

    getExpiryFromUrl(signedUrl) {
        try {
            const url = new URL(signedUrl);
            const expires = url.searchParams.get("Expires") || url.searchParams.get("X-Amz-Expires");
            if (expires) {
                return parseInt(expires) * 1000;
            }
            return null;
        } catch (error) {
            console.error("[S3Controller] Error extracting expiry from URL:", error);
            return null;
        }
    }

    async fetchCloudfrontSignedUrl(cacheKey, requestParams) {
        try {
            const cachedUrl = await this.getCachedUrl(cacheKey);
            if (cachedUrl) {
                return cachedUrl;
            }

            const response = await axiosInstance.get("/s3/cloudfront/signedUrl", {
                params: requestParams,
            });

            const signedUrl = response.data.signedUrl;

            if (signedUrl) {
                const expiresAt = this.getExpiryFromUrl(signedUrl);

                if (expiresAt) {
                    await idb.addItems(this.cacheStore, [
                        {
                            _id: cacheKey,
                            url: signedUrl,
                            expiresAt: expiresAt,
                            timestamp: Date.now()
                        },
                    ]);
                } else {
                    console.warn(`[S3Controller] Could not extract expiry from URL, not caching: ${cacheKey}`);
                }
            }

            return signedUrl;
        } catch (error) {
            console.error("Error fetching CloudFront signed URL:", error);
            throw error;
        }
    }

    fetchUrl(artifact, linkType = undefined) {
        return `${environment.VITE_API_URL}/api/artifacts${linkType ? `/${linkType}` : ""}/link/${artifact._id}${linkType === "video" ? `.mp4` : ""}`;
    }

    fetchPreviewUrl(artifact) {
        return this.fetchUrl(artifact, "thumbnail_image");
    }

    async fetchArtifactUrl(artifactId, type) {
        const cacheKey = `${artifactId}_${type}`;
        const requestParams = { id: artifactId, type: type };
        return this.fetchCloudfrontSignedUrl(cacheKey, requestParams);
    }

    async fetchAssetUrl(key) {
        const cacheKey = `asset_${key}`;
        const requestParams = { key: key };
        return this.fetchCloudfrontSignedUrl(cacheKey, requestParams);
    }
}
const s3Controller = new S3Controller();

export default s3Controller;
