import environment from "../../environment.js";
import axiosInstance from "../axios.js";
import idb from "../indexedDB.js";

class S3Controller {
    constructor() {
        this.cacheStore = 's3_url_cache';
    }

    async cleanExpiredUrls() {
        try {
            const allCachedItems = await idb.getItems(this.cacheStore);
            const expiredIds = [];
            const now = Date.now();

            for (const item of allCachedItems) {
                if (item.expiresAt && now >= item.expiresAt) {
                    // Check if URL is still cached in browser before removing
                    const isInBrowserCache = await this.checkBrowserCache(item.url);

                    if (!isInBrowserCache) {
                        // Only remove if not in browser cache
                        expiredIds.push(item._id);
                    } else {
                        console.log(`[S3Controller] Keeping expired URL ${item._id} - still in browser cache`);
                    }
                }
            }

            if (expiredIds.length > 0) {
                for (const id of expiredIds) {
                    await idb.deleteItem(this.cacheStore, id);
                }
                console.log(`[S3Controller] Cleaned ${expiredIds.length} expired URLs`);
            }
        } catch (error) {
            console.error('[S3Controller] Error cleaning expired URLs:', error);
        }
    }

    getExpiryFromUrl(signedUrl) {
        try {
            const url = new URL(signedUrl);
            const expires = url.searchParams.get('Expires');
            if (expires) {
                return parseInt(expires) * 1000;
            } else {
                return 24 * 60 * 60 * 1000;
            }
        } catch (error) {
            console.error('[S3Controller] Error extracting expiry from URL:', error);
        }
        return null;
    }

    // Check if URL is still cached in browser cache
    async checkBrowserCache(url) {
        try {
            const response = await fetch(url, {
                method: 'HEAD',
                cache: 'only-if-cached',
                mode: 'cors'
            });
            console.log('[S3Controller] checkBrowserCache', response);
            return response.ok;
        } catch (error) {
            console.log('[S3Controller] checkBrowserCache', error);
            // If fetch fails with 'only-if-cached', it means not in cache
            return false;
        }
    }

    async fetchCloudfrontSignedUrl(artifactId, type) {
        try {
            await this.cleanExpiredUrls();

            const cacheKey = `${artifactId}_${type}`;

            const allCachedItems = await idb.getItems(this.cacheStore);
            const cachedData = allCachedItems.find(item => item._id === cacheKey);

            if (cachedData && cachedData.expiresAt && Date.now() < cachedData.expiresAt) {
                return cachedData.url;
            }

            const response = await axiosInstance.get('/s3/cloudfront/signedUrl', {
                params: { id: artifactId, type: type }
            });

            const signedUrl = response.data.signedUrl;

            if (signedUrl) {
                const expiresAt = this.getExpiryFromUrl(signedUrl);
                await idb.addItems(this.cacheStore, [{
                    _id: cacheKey,
                    url: signedUrl,
                    expiresAt: expiresAt,
                    timestamp: Date.now()
                }]);
            }

            return signedUrl;
        } catch (error) {
            console.error('Error fetching CloudFront signed URL:', error);
            throw error;
        }
    }

    async fetchCloudfrontAssetUrl(key) {
        try {
            await this.cleanExpiredUrls();

            const cacheKey = `asset_${key}`;

            const allCachedItems = await idb.getItems(this.cacheStore);
            const cachedData = allCachedItems.find(item => item._id === cacheKey);

            if (cachedData && cachedData.expiresAt && Date.now() < cachedData.expiresAt) {
                return cachedData.url;
            }

            const response = await axiosInstance.get('/s3/cloudfront/signedUrl', {
                params: { key: key }
            });

            const signedUrl = response.data.signedUrl;

            if (signedUrl) {
                const expiresAt = this.getExpiryFromUrl(signedUrl);
                await idb.addItems(this.cacheStore, [{
                    _id: cacheKey,
                    url: signedUrl,
                    expiresAt: expiresAt,
                    timestamp: Date.now()
                }]);
            }

            return signedUrl;
        } catch (error) {
            console.error('Error fetching CloudFront asset URL:', error);
            throw error;
        }
    }

    fetchUrl(artifact, linkType = undefined) {
        return `${environment.VITE_API_URL}/api/artifacts${linkType ? `/${linkType}` : ""}/link/${artifact._id}${linkType === "video" ? `.mp4` : ""}`;
    }

    fetchPreviewUrl(artifact) {
        return this.fetchUrl(artifact, "thumbnail_image");
    }
}
const s3Controller = new S3Controller();

export default s3Controller;
