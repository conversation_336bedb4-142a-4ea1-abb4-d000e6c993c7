import ReactDOM from "react-dom/client";
import "./index.css";
import Router from "./Router.jsx";
import { isEnvironment } from "./utils.js";
import environment from "../environment.js";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import dayjs from "dayjs";

dayjs.extend(utc);
dayjs.extend(timezone);

// Remove the splash screen once the app is rendered
function removeSplashScreen() {
    const splash = document.getElementById("splash");
    if (splash) {
        splash.style.transition = "opacity 0.5s";
        splash.style.opacity = "0";
        // setTimeout(() => {
        splash.remove();
        // }, 250); // Delay removal to allow fade-out
    }
}

if (isEnvironment(environment.production)) {
    console.log = () => {};
}

ReactDOM.createRoot(document.getElementById("root")).render(
    <Router />,
    removeSplashScreen(), // Remove splash screen after rendering the app
);
