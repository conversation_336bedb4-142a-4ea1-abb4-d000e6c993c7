const express = require("express");
const isAuthenticated = require("../middlewares/auth");
const hasPermission = require("../middlewares/hasPermission");
const { validateData } = require("../middlewares/validator");
const { body, param, query } = require("express-validator");
const { validateError } = require("../utils/functions");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const { permissions } = require("../utils/permissions");
const router = express.Router();
const vesselManagementService = require("../services/VesselManagement.service");
const vesselService = require("../services/Vessel.service");
const { upload, handleMulterError } = require("../middlewares/multerConfig");

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSEL_MANAGEMENT),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageVessels]),
    validateData.bind(this, [
        query("page").optional().isInt({ min: 1 }).toInt(),
        query("limit").optional().isInt({ min: 1, max: 100 }).toInt(),
        query("search").optional().isString().trim(),
    ]),
    async (req, res) => {
        try {
            const { page = 1, limit = 10, search = "" } = req.query;
            const result = await vesselManagementService.fetchAll({ page, limit, search });
            res.json(result);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/unitIds",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSEL_UNIT_IDS),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageVessels]),
    validateData.bind(this, [
        query("regions")
            .isString()
            .optional()
            .customSanitizer((v) => v.split(",").map((v) => v.trim())),
    ]),
    async (req, res) => {
        try {
            const { regions } = req.query;

            const vesselsList = await vesselService.fetchAll({ regions });

            const unitDetails = vesselsList.map((vessel) => ({
                unit_id: vessel.unit_id,
                name: vessel.name,
                region: vessel.region,
            }));

            res.json(unitDetails);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/:id",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSEL_MANAGEMENT_BY_ID),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageVessels]),
    validateData.bind(this, [param("id").isMongoId().withMessage("Invalid vessel ID")]),
    async (req, res) => {
        try {
            const { id } = req.params;
            const vessel = await vesselManagementService.findById({ id });

            if (!vessel) {
                return res.status(404).json({ message: "Vessel not found" });
            }

            res.json(vessel);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_VESSEL_MANAGEMENT),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageVessels]),
    upload.single("thumbnail_file"),
    handleMulterError,
    validateData.bind(this, [
        body("name").isString().trim().notEmpty().withMessage("Name is required and must be a non-empty string"),
        body("unit_id").optional().isString().withMessage("Unit ID must be a string"),
        body("is_active").optional().isBoolean().withMessage("is_active must be a boolean"),
    ]),
    async (req, res) => {
        try {
            const { name, unit_id, is_active } = req.body;
            const created_by = req.user._id;
            const thumbnail_file = req.file;

            const vessel = await vesselManagementService.create({
                name,
                thumbnail_file,
                unit_id,
                is_active,
                created_by,
            });

            res.status(201).json(vessel);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.put(
    "/:id",
    assignEndpointId.bind(this, endpointIds.UPDATE_VESSEL_MANAGEMENT),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageVessels]),
    upload.single("thumbnail_file"),
    handleMulterError,
    validateData.bind(this, [
        param("id").isMongoId().withMessage("Invalid vessel ID"),
        body("name").optional().isString().trim().notEmpty().withMessage("Name must be a non-empty string"),
        body("unit_id").optional().isString().withMessage("Unit ID must be a string"),
        body("is_active").optional().isBoolean().withMessage("is_active must be a boolean"),
    ]),
    async (req, res) => {
        try {
            const { id } = req.params;
            const { name, unit_id, is_active } = req.body;
            const thumbnail_file = req.file;

            const vessel = await vesselManagementService.update({
                id,
                name,
                thumbnail_file,
                unit_id,
                is_active,
            });

            res.json(vessel);
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;
