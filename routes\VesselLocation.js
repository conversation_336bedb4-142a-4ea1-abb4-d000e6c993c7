const express = require("express");
const { validateData } = require("../middlewares/validator");
const { body } = require("express-validator");
const { validateError, canAccessUnit } = require("../utils/functions");
const limitPromise = require("../modules/pLimit");
const { default: mongoose } = require("mongoose");
const isAuthenticated = require("../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const db = require("../modules/db");
const router = express.Router();
const compression = require("compression");
const vesselService = require("../services/Vessel.service");
const { getLinkedUnits } = require("../utils/linkedUnits");

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);
router.use(compression());

router.post(
    "/:vesselName",
    assignEndpointId.bind(this, endpointIds.FETCH_COORDINATES),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                // the below cannot be verified by test cases
                // param('vesselName').isString().notEmpty().withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                body("lastKnown")
                    .customSanitizer((v) => Number(v))
                    .isInt({ min: 0, max: 1 })
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("startTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("endTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("excludeIds")
                    .isArray()
                    .bail()
                    .customSanitizer((v) => v.map((id) => mongoose.Types.ObjectId(id)))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const { vesselName } = req.params;
            const { startTimestamp, endTimestamp, lastKnown, excludeIds } = req.body;
            console.log(`/vesselLocations ${vesselName}`, startTimestamp, endTimestamp, lastKnown);

            if (endTimestamp && !startTimestamp) {
                return res.status(400).json({ message: "startTimestamp is required when endTimestamp is provided" });
            }

            const streamInfo = await vesselService.fetchSingle({ unitId: vesselName });
            if (!streamInfo) return res.status(400).json({ message: "Vessel does not exist" });

            if (!canAccessUnit(req, streamInfo)) {
                return res.status(403).json({ message: `Cannot access coordinates for '${vesselName}'` });
            }

            const ts = new Date().getTime();
            const linkedUnits = getLinkedUnits(vesselName);
            const collections = linkedUnits.map((unitId) => db.qm.collection(`${unitId}_location`));
            if (!collections.length) return res.status(400).json({ message: "Invalid vesselName" });

            const query = {};
            if (startTimestamp) {
                const endTime = endTimestamp || Date.now();
                query.timestamp = { $gt: new Date(startTimestamp), $lt: new Date(endTime) };
            }
            if (excludeIds) query._id = { $nin: excludeIds };

            var locations = await limitPromise(async () => {
                if (isClosed) return res.end();
                console.log(`/vesselLocations ${vesselName} querying DB`);
                if (lastKnown) {
                    const locations = (
                        await Promise.all(
                            collections.map((collection) =>
                                collection.findOne(
                                    {},
                                    {
                                        projection: { _id: 1, latitude: 1, longitude: 1, groundSpeed: 1, isStationary: 1, timestamp: 1 },
                                        sort: { timestamp: -1 },
                                    },
                                ),
                            ),
                        )
                    )
                        .flat()
                        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
                    return locations[0];
                } else {
                    const locations = (
                        await Promise.all(
                            collections.map((collection) => {
                                const cursor = collection.find(query, {
                                    projection: { _id: 1, latitude: 1, longitude: 1, groundSpeed: 1, isStationary: 1, timestamp: 1 },
                                });

                                if (isSwagger) {
                                    cursor.limit(20);
                                }

                                return cursor.toArray();
                            }),
                        )
                    )
                        .flat()
                        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
                    return locations;
                }
            });

            console.log(`/vesselLocations ${vesselName} time taken to query ${new Date().getTime() - ts}`);
            console.log(`/vesselLocations ${vesselName} received ${(locations && locations.length) || 1} coordinates`);
            console.log(`/vesselLocations ${vesselName} time taken to respond ${new Date().getTime() - ts}`);

            if (isClosed) return res.end();

            res.json(locations);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Vessel Locations
 *   description: Fetch vessel location data
 * components:
 *   schemas:
 *     VesselLocation:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique identifier for the vessel location
 *           example: "66e75fc080f445d4f062b294"
 *         latitude:
 *           type: number
 *           format: float
 *           description: Latitude of the vessel's location
 *           example: 8.333152
 *         longitude:
 *           type: number
 *           format: float
 *           description: Longitude of the vessel's location
 *           example: 117.2075473
 *         groundSpeed:
 *           type: number
 *           format: float
 *           description: The ground speed of the vessel in knots
 *           example: 0.023
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the location data was recorded
 *           example: "2024-09-15T22:29:20.555Z"
 *         isStationary:
 *           type: boolean
 *           description: Indicates whether the vessel is stationary at this location
 *           example: false
 */

/**
 * @swagger
 * /vesselLocations/{vesselName}:
 *   post:
 *     summary: Fetch vessel location data
 *     description: Fetch vessel location data for a given vessel, with optional parameters for filtering by timestamp range, excluding specific IDs, and fetching the last known location.<br/>Rate limited to 20 requests every 5 seconds
 *     tags: [Vessel Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: vesselName
 *         required: true
 *         description: Name of the vessel to fetch location data for
 *         schema:
 *           type: string
 *           example: prototype-37
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               lastKnown:
 *                 type: integer
 *                 required: false
 *                 description: Flag to only fetch last known location (0 for false, 1 for true)
 *                 example: 0
 *               startTimestamp:
 *                 type: integer
 *                 required: false
 *                 description: Start unix timestamp in milliseconds for filtering location data
 *                 example: 1727136000000
 *               endTimestamp:
 *                 type: integer
 *                 required: false
 *                 description: End unix timestamp in milliseconds for filtering location data
 *                 example: 1727222400000
 *               excludeIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 required: false
 *                 description: Array of document IDs to exclude from the result
 *                 example: ["64c6b0f7f83c8b57fa5f3242", "64c6b0f7f83c8b57fa5f3243"]
 *     responses:
 *       200:
 *         description: An array of vessel location coordinates
 *         content:
 *           text/html:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/VesselLocation'
 *       400:
 *         description: Invalid request or vessel name
 *       403:
 *         description: Cannot access this unit
 *       500:
 *         description: Internal server error
 */
