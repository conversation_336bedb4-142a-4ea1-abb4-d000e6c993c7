import { useState, useRef, useEffect } from "react";
import { Box, CircularProgress, Typography, Alert, IconButton, Toolt<PERSON> } from "@mui/material";
import { PhotoCamera, Close } from "@mui/icons-material";
import { alpha } from "@mui/material/styles";
import theme from "../theme";
import s3Controller from "../controllers/S3.controller";

const S3ImageUpload = ({
    value,
    onChange,
    onRemove, // New prop for handling removal
    label = "Upload Image",
    maxSizeBytes = 5 * 1024 * 1024,
    acceptedTypes = "image/*",
    disabled = false,
    error = false,
    helperText = "",
}) => {
    const [uploading, setUploading] = useState(false);
    const [removing, setRemoving] = useState(false);
    const [uploadError, setUploadError] = useState("");
    const [preview, setPreview] = useState("");
    const [loadingPreview, setLoadingPreview] = useState(true);
    const fileInputRef = useRef(null);

    useEffect(() => {
        const loadExistingImage = async () => {
            setLoadingPreview(true);
            setUploadError("");

            try {
                if (typeof value === "string" && value) {
                    if (!value.startsWith("http") && value.includes("/")) {
                        const imageUrl = await s3Controller.fetchSignedUrl(value);

                        if (imageUrl) {
                            setPreview(imageUrl);
                        } else {
                            setUploadError("Failed to load existing image");
                        }
                    } else if (value.startsWith("http")) {
                        setPreview(value);
                    }
                } else if (value === null || value === undefined) {
                    setPreview("");
                }
            } catch (error) {
                console.error("Error loading existing image:", error);
                setUploadError("Failed to load existing image");
            } finally {
                setLoadingPreview(false);
            }
        };

        if (!(value instanceof File)) {
            loadExistingImage();
        }
    }, [value]);

    const getPreviewUrl = () => {
        return preview || (typeof value === "string" && value ? value : "");
    };

    const validateFile = (file) => {
        if (!file) return "No file selected";

        if (!file.type.startsWith("image/")) {
            return "Please select an image file";
        }

        if (file.size > maxSizeBytes) {
            const maxSizeMB = maxSizeBytes / (1024 * 1024);
            return `File size must be less than ${maxSizeMB}MB`;
        }

        return null;
    };

    const processFile = async (file) => {
        try {
            setUploading(true);
            setUploadError("");

            const reader = new FileReader();
            reader.onload = (e) => {
                const dataUrl = e.target.result;
                setPreview(dataUrl);
                onChange(file);
                setUploading(false);
            };

            reader.onerror = () => {
                setUploadError("Failed to read file");
                setUploading(false);
            };

            reader.readAsDataURL(file);
        } catch (error) {
            console.error("File processing error:", error);
            setUploadError(error.message || "Failed to process image");
            setUploading(false);
        }
    };

    const handleFileSelect = async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        const validationError = validateFile(file);
        if (validationError) {
            setUploadError(validationError);
            return;
        }

        await processFile(file);
    };

    const handleClick = () => {
        if (!disabled && fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    const handleRemove = async (e) => {
        e.stopPropagation();

        if (onRemove) {
            // If onRemove prop is provided, use it for API call
            try {
                setRemoving(true);
                setUploadError("");
                await onRemove();
                // Only clear frontend state after successful API call
                setPreview("");
                onChange(null);
                if (fileInputRef.current) {
                    fileInputRef.current.value = "";
                }
            } catch (error) {
                console.error("Error removing image:", error);
                setUploadError("Failed to remove image. Please try again.");
            } finally {
                setRemoving(false);
            }
        } else {
            // Fallback to frontend-only removal if no onRemove prop
            setPreview("");
            setUploadError("");
            onChange(null);
            if (fileInputRef.current) {
                fileInputRef.current.value = "";
            }
        }
    };

    return (
        <Box sx={{ width: "100%" }}>
            <Typography variant="body2" sx={{ mb: 1, color: "#FFFFFF" }}>
                {label}
            </Typography>

            <input
                ref={fileInputRef}
                type="file"
                accept={acceptedTypes}
                onChange={handleFileSelect}
                style={{ display: "none" }}
                disabled={disabled || uploading}
            />

            <Box
                sx={{
                    border: `2px dashed ${error ? theme.palette.error.main : alpha(theme.palette.custom.mainBlue, 0.3)}`,
                    borderRadius: 2,
                    p: 2,
                    pt: loadingPreview ? 6 : 2,
                    pb: loadingPreview ? 6 : 2,
                    textAlign: "center",
                    cursor: disabled || uploading ? "not-allowed" : "pointer",
                    backgroundColor: alpha(theme.palette.custom.mainBlue, 0.05),
                    transition: "all 0.2s ease-in-out",
                    "&:hover":
                        !disabled && !uploading
                            ? {
                                borderColor: theme.palette.custom.mainBlue,
                                backgroundColor: alpha(theme.palette.custom.mainBlue, 0.1),
                            }
                            : {},
                }}
                onClick={handleClick}
            >
                {getPreviewUrl() ? (
                    <Box sx={{ position: "relative" }}>
                        {loadingPreview ? (
                            <Box
                                sx={{
                                    position: "absolute",
                                    top: 0,
                                    left: 0,
                                    right: 0,
                                    bottom: 0,
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}
                            >
                                <CircularProgress size={40} />
                            </Box>
                        ) : (
                            <>
                                <Box
                                    component="img"
                                    src={getPreviewUrl()}
                                    alt="Preview"
                                    sx={{
                                        maxWidth: "100%",
                                        maxHeight: 200,
                                        borderRadius: 1,
                                        objectFit: "contain",
                                    }}
                                />
                                {!disabled && (
                                    <Tooltip title={removing ? "Removing..." : "Remove image"}>
                                        <IconButton
                                            onClick={handleRemove}
                                            disabled={removing || uploading}
                                            sx={{
                                                position: "absolute",
                                                top: 8,
                                                right: 8,
                                                backgroundColor: alpha("#000000", 0.6),
                                                color: "#FFFFFF",
                                                "&:hover": {
                                                    backgroundColor: alpha("#000000", 0.8),
                                                },
                                                "&:disabled": {
                                                    backgroundColor: alpha("#000000", 0.3),
                                                    color: alpha("#FFFFFF", 0.5),
                                                },
                                                width: 32,
                                                height: 32,
                                            }}
                                            size="small"
                                        >
                                            {removing ? (
                                                <CircularProgress size={16} color="inherit" />
                                            ) : (
                                                <Close fontSize="small" />
                                            )}
                                        </IconButton>
                                    </Tooltip>
                                )}
                            </>
                        )}
                    </Box>
                ) : (
                    <Box>
                        {uploading ? (
                            <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", gap: 2 }}>
                                <CircularProgress size={40} />
                                <Typography variant="body2" color="#FFFFFF">
                                    Uploading...
                                </Typography>
                            </Box>
                        ) : (
                            <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", gap: 2 }}>
                                <PhotoCamera sx={{ fontSize: 48, color: theme.palette.custom.mainBlue }} />
                                <Typography variant="body2" color="#FFFFFF">
                                    Click to upload image
                                </Typography>
                                <Typography variant="caption" color={alpha("#FFFFFF", 0.7)}>
                                    Supports: JPG, PNG, GIF (Max: {Math.round(maxSizeBytes / (1024 * 1024))}MB)
                                </Typography>
                            </Box>
                        )}
                    </Box>
                )}
            </Box>

            {(uploadError || helperText) && (
                <Box sx={{ mt: 1 }}>
                    {uploadError && (
                        <Alert severity="error" sx={{ mb: 1 }}>
                            {uploadError}
                        </Alert>
                    )}
                    {helperText && !uploadError && (
                        <Typography variant="caption" color={alpha("#FFFFFF", 0.7)}>
                            {helperText}
                        </Typography>
                    )}
                </Box>
            )}
        </Box>
    );
};

export default S3ImageUpload;
