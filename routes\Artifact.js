const express = require("express");
const { validateError, canAccessUnit, fileNameTimestamp, generateZip, streamToBuffer, generateTimeSeries } = require("../utils/functions");
const db = require("../modules/db");
const { validateData } = require("../middlewares/validator");
const { body, param, query } = require("express-validator");
const limitPromise = require("../modules/pLimit");
const { default: mongoose } = require("mongoose");
const isAuthenticated = require("../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const router = express.Router();
const compression = require("compression");
const { permissions } = require("../utils/permissions");
const ArtifactFavourites = require("../models/ArtifactFavourites");
const vesselService = require("../services/Vessel.service");
const { getObjectStream, s3, buildThumbnailImage, getS3Object, getItemObjectPart } = require("../modules/awsS3");
const redisClient = require("../services/Redis.service");
const { Readable } = require("stream");
const dayjs = require("dayjs");
const { defaultDateTimeFormat } = require("../utils/timezonesList");
const { getLinkedUnits, populateLinkedUnitsInArray } = require("../utils/linkedUnits");

router.use(compression());

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
    validate: {
        xForwardedForHeader: false,
        default: true,
    },
});

const conditionalApiLimiter = (req, res, next) => {
    if (req.path.match(/\/link\//) || req.header("Fetch-From") === "dashboard") {
        // Skip rate limiting for excluded paths
        next();
    } else {
        // Apply rate limiting for other paths
        apiLimiter(req, res, next);
    }
};

router.use("/", conditionalApiLimiter);

router.get("/filters", assignEndpointId.bind(this, endpointIds.FETCH_ARTIFACT_FILTERS), isAuthenticated, async (req, res) => {
    try {
        const countryFlags = await db.qm.collection("notification_flags").find().toArray();
        const superCategories = await db.qm.collection("notification_categories").find().project({ name: 1, code: 1 }).toArray();

        const filterItems = {
            superCategories: superCategories.map((itm) => itm.name),
            countryFlags: countryFlags.map((itm) => {
                return { name: itm.name, code: itm.code };
            }),
        };

        res.status(200).json(filterItems);
    } catch (err) {
        validateError(err, res);
    }
});

router.post(
    "/:vesselName",
    assignEndpointId.bind(this, endpointIds.FETCH_ARTIFACTS),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                // param('vesselName').isString().notEmpty().withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                body("startTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("endTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("excludeIds")
                    .isArray()
                    .bail()
                    .customSanitizer((v) => v.map((id) => mongoose.Types.ObjectId(id)))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const ts = new Date().getTime();
            const { vesselName } = req.params;
            const { startTimestamp, endTimestamp, excludeIds } = req.body;
            console.log(`/artifacts ${vesselName}`, startTimestamp, endTimestamp);

            if (endTimestamp && !startTimestamp) {
                return res.status(400).json({ message: "startTimestamp is required when endTimestamp is provided" });
            }

            const streamInfo = await vesselService.fetchSingle({ unitId: vesselName });
            if (!streamInfo) return res.status(400).json({ message: "Vessel does not exist" });

            if (!canAccessUnit(req, streamInfo)) {
                return res.status(403).json({ message: `Cannot access artifacts for '${vesselName}'` });
            }

            const query = {};
            query.unit_id = { $in: getLinkedUnits(vesselName) };
            if (startTimestamp) {
                const endTime = endTimestamp || Date.now();
                query.timestamp = { $gt: new Date(startTimestamp), $lt: new Date(endTime) };
            }
            if (excludeIds) query._id = { $nin: excludeIds };
            query.location = { $ne: null };
            query.vessel_presence = true;
            query.super_category = { $ne: null };

            const artifacts = await limitPromise(async () => {
                if (isClosed) return res.end();
                console.log(`/artifacts ${vesselName} querying DB`);
                const cursor = db.qmai.collection("analysis_results").find(query, {
                    projection: {
                        _id: 1,
                        unit_id: 1,
                        bucket_name: 1,
                        aws_region: 1,
                        image_path: 1,
                        video_path: 1,
                        location: 1,
                        category: 1,
                        super_category: 1,
                        size: 1,
                        color: 1,
                        others: 1,
                        timestamp: 1,
                        text_extraction: 1,
                        weapons: 1,
                        imo_number: 1,
                    },
                });

                if (isSwagger) {
                    cursor.limit(20);
                }

                return await cursor.toArray();
            });
            console.log(`/artifacts ${vesselName} time taken to query ${new Date().getTime() - ts}`);
            console.log(`/artifacts ${vesselName} received ${artifacts?.length} artifacts`);
            if (isClosed) return res.end();
            res.json(artifacts);
            console.log(`/artifacts ${vesselName} time taken to respond ${new Date().getTime() - ts}`);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_PAGINATED_ARTIFACTS),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                body("excludeIds")
                    .isArray()
                    .bail()
                    .customSanitizer((v) => v.map((id) => mongoose.Types.ObjectId(id)))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("page").isInt({ min: 1 }).default(1).withMessage("Page must be a positive integer"),
                body("pageSize").isInt({ min: 1 }).default(20).withMessage("Page size must be a positive integer"),
                body("filters").isObject().withMessage("Filters must be a valid JSON object").optional(),
                body("favourites")
                    .isInt({ min: 0, max: 1 })
                    .optional()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const ts = new Date().getTime();
            const { excludeIds, page, pageSize, filters, favourites } = req.body;
            console.log(`/artifacts`);

            let allowedUnits = [];

            const query = {};
            if (excludeIds) query._id = { $nin: excludeIds };
            query.location = { $ne: null };
            query.vessel_presence = true;
            // TODO: handle for API key
            if (req.user) {
                if (!req.user.permissions.find((p) => p.permission_id === permissions.accessAllUnits)) {
                    allowedUnits = populateLinkedUnitsInArray(req.user.allowed_units.map((v) => v.unit_id));
                    query.unit_id = { $in: allowedUnits };
                } else {
                    const allUnits = await vesselService.fetchAll({});
                    allowedUnits = populateLinkedUnitsInArray(allUnits.map((v) => v.unit_id));
                    query.unit_id = { $in: allowedUnits };
                }
            }

            if (filters) {
                const { startTime, endTime, categories, id, colors, sizes, type, unit_ids, country_flags } = filters;
                if (id) {
                    query._id = mongoose.Types.ObjectId(id);
                } else {
                    if (startTime && endTime) query.timestamp = { $gt: new Date(startTime), $lt: new Date(endTime) };
                    if (categories) query.super_category = { $in: categories };
                    if (colors) query.color = { $in: colors };
                    if (sizes) query.size = { $in: sizes };
                    if (unit_ids) {
                        // TODO: handle for API key
                        if (
                            req.user &&
                            !req.user.permissions.find((p) => p.permission_id === permissions.accessAllUnits) &&
                            unit_ids.some((id) => !allowedUnits.includes(id))
                        ) {
                            return res.status(403).json({ message: `Cannot access artifacts for '${unit_ids}'` });
                        }
                        query.unit_id = { $in: populateLinkedUnitsInArray(unit_ids) };
                    }
                    if (country_flags) query.country_flag = { $in: country_flags };
                }
                if (type) {
                    if (type === "video") {
                        query.video_path = { $exists: true, $ne: null };
                    } else if (type === "image") {
                        query.video_path = { $exists: false };
                    }
                }
            }

            const skip = (page - 1) * pageSize;
            const limit = pageSize;

            const totalCount = await db.qmai.collection("analysis_results").countDocuments(query);
            const artifacts = await limitPromise(async () => {
                if (isClosed) return res.end();
                console.log(`/artifacts querying DB`);
                return await db.qmai
                    .collection("analysis_results")
                    .find(query)
                    .sort({ timestamp: -1 }) // Sort by timestamp in descending order
                    .project({
                        _id: 1,
                        unit_id: 1,
                        bucket_name: 1,
                        image_path: 1,
                        video_path: 1,
                        location: 1,
                        category: 1,
                        super_category: 1,
                        size: 1,
                        color: 1,
                        weapons: 1,
                        others: 1,
                        timestamp: 1,
                        onboard_vessel_name: 1,
                        country_flag: 1,
                        aws_region: 1,
                        text_extraction: 1,
                        imo_number: 1,
                    })
                    .skip(skip)
                    .limit(limit)
                    .toArray();
            });
            console.log(`/artifacts time taken to query ${new Date().getTime() - ts}`);
            console.log(`/artifacts received ${artifacts?.length} artifacts`);
            console.log("userid", req.user._id);
            let favouritesArtifacts;
            // this condition will not work with API Key
            if (favourites && req.user) {
                const rawFavourites = await ArtifactFavourites.find({ user_id: req.user._id }).select("_id artifact_id");
                const artifactIds = rawFavourites.map((fav) => fav.artifact_id);
                const allowedArtifactIds = await db.qmai
                    .collection("analysis_results")
                    .find({
                        _id: { $in: artifactIds },
                        unit_id: { $in: allowedUnits },
                    })
                    .project({ _id: 1 })
                    .toArray();
                const allowedIdsSet = new Set(allowedArtifactIds.map((a) => a._id.toString()));

                favouritesArtifacts = rawFavourites
                    .filter((fav) => allowedIdsSet.has(fav.artifact_id.toString()))
                    .map((fav) => ({
                        _id: fav._id,
                        artifact_id: fav.artifact_id,
                    }));
            }

            if (isClosed) return res.end();
            const data = {
                artifacts,
                page,
                pageSize,
                totalCount,
                favouritesArtifacts,
            };
            res.json(data);
            console.log(`/artifacts time taken to respond ${new Date().getTime() - ts}`);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

router.post("/:id/download", assignEndpointId.bind(this, endpointIds.DOWNLOAD_ARTIFACT), isAuthenticated, async (req, res) => {
    try {
        const { id } = req.params;

        const artifact = await db.qmai.collection("analysis_results").findOne({ _id: mongoose.Types.ObjectId(id) });

        const getMetadata = () => {
            return `
Timestamp: ${dayjs(artifact.timestamp).format(req.user.date_time_format || defaultDateTimeFormat)} 
Category: ${artifact.super_category}
Sub Category: ${artifact.category}
Color: ${artifact.color}
Size: ${artifact.size}
Location: ${(artifact.location.coordinates || [])[0]}, ${(artifact.location.coordinates || [])[1]}
Others: ${artifact.others}
        `;
        };

        const filenameMask = `${fileNameTimestamp()} - ${(artifact.onboard_vessel_name || artifact.unit_id).replace(/ /g, "_")}`;

        const mediaKey = artifact.video_path || artifact.image_path;
        const mediaPathParts = mediaKey.split(".");
        const extension = mediaPathParts[mediaPathParts.length - 1];

        const mediaData = await getObjectStream(artifact.bucket_name, artifact.aws_region, mediaKey);

        const filesData = [
            {
                name: `${filenameMask}.${extension}`,
                content: mediaData,
            },
            {
                name: `${filenameMask}.txt`,
                content: getMetadata(),
            },
        ];

        const zip = generateZip(filesData);
        const stream = zip.generateNodeStream({ type: "nodebuffer", streamFiles: true });

        //Using pipeline for better error handling.
        res.setHeader("Content-Type", "application/zip");
        res.setHeader("Content-Disposition", `attachment; filename="${filenameMask}.zip"`);

        await new Promise((resolve, reject) => {
            mediaData.on("error", (err) => {
                console.error("S3 stream error:", err);
                reject(err);
            });

            stream
                .pipe(res)
                .on("finish", resolve)
                .on("error", (e) => {
                    reject(e);
                });
        });
    } catch (err) {
        validateError(err, res);
    }
});

const responseWithImage = (res, imageObject, filename) => {
    // Set Content-Type and other headers from S3 object (important for correct file handling)
    res.setHeader("Content-Type", imageObject.ContentType || (() => (filename.split(".").pop() === "mp4" ? "video/mp4" : "image/jpeg"))());

    // Add cache control headers
    res.setHeader("Cache-Control", "public, max-age=31536000"); // Cache for 1 year
    res.setHeader("ETag", imageObject.ETag || `"${filename}"`);

    if (imageObject.ContentLength) {
        res.setHeader("Content-Length", imageObject.ContentLength);
    }

    // I've commented out the Content-Disposition header to prevent the API from forcing a download of the artifact.
    // res.setHeader("Content-Disposition", `attachment; filename="${filename}"`);

    const stream = imageObject.createReadStream();

    res.on("error", (err) => {
        console.error("Response Stream Error:", err);
        stream.destroy();
    });

    res.on("finish", () => {
        console.log("Piping finished successfully.");
    });

    res.on("close", () => {
        console.log("Response connection closed.");
    });

    stream
        .pipe(res)
        .on("finish", () => "Finished")
        .on("error", (e) => console.error("S3 stream error:", e.message));
};

router.get(
    "/:urlType?/link/:id.:ext?",
    assignEndpointId.bind(this, endpointIds.FETCH_URL_FOR_ALERT),
    validateData.bind(this, [
        param("urlType")
            .optional()
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((urlType) => {
                if (urlType !== "image" && urlType !== "video" && urlType !== "thumbnail_image") {
                    throw new Error(`Not supported type of the url type "${urlType}"`);
                }
                return true;
            }),
        param("id")
            .isMongoId()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
    ]),
    async (req, res) => {
        const { urlType, id } = req.params;

        const thumbnailIsRequested = urlType?.startsWith("thumbnail_image");
        const imageKey = `thumb_image#${id}`;
        console.log("imageKey", imageKey);

        //INFO: caching only thumbnails
        if (thumbnailIsRequested) {
            const cachedImage = await redisClient.get(imageKey);

            if (cachedImage) {
                try {
                    const cachedImageObj = JSON.parse(cachedImage);

                    responseWithImage(
                        res,
                        {
                            ...cachedImageObj,
                            createReadStream: () => Readable.from(Buffer.from(cachedImageObj.Content, "hex")),
                        },
                        cachedImageObj.filename,
                    );

                    return;
                } catch (err) {
                    console.error(err);
                    await redisClient.del(imageKey);
                }
            }
        }

        const artifact = await db.qmai.collection("analysis_results").findOne({ _id: mongoose.Types.ObjectId(id) });

        if (artifact) {
            if (urlType === "video" && !artifact.video_path) {
                return res.status(404).send("Video is not found");
            }

            const { bucket_name: Bucket, aws_region: region } = artifact;
            s3.config.update({ region });

            let key = artifact[`${urlType}_path`] || artifact.image_path || artifact.video_path; // artifact[`${urlType}_path`]  || artifact.video_path || artifact.image_path;
            let s3Object = undefined;

            if (thumbnailIsRequested) {
                if (!artifact.thumbnail_image_path) {
                    s3Object = await buildThumbnailImage(Bucket, region, key, artifact.unit_id);
                }
            }

            try {
                const isVideo = key.endsWith(".mp4");
                const isCachable = !isVideo || process.env.ARTIFACT_THUMBNAILS_CACHE_VIDEO === "true";
                const isAcceptableForCatching = thumbnailIsRequested && isCachable;

                if (isVideo && !isAcceptableForCatching) {
                    return getItemObjectPart(req, res, Bucket, region, key);
                } else {
                    s3Object =
                        s3Object ??
                        (await (async function () {
                            const targetBucket = key === artifact.thumbnail_image_path ? process.env.AWS_COMPRESSED_ITEMS_BUCKET : Bucket;
                            const targetRegion = key === artifact.thumbnail_image_path ? process.env.AWS_COMPRESSED_ITEMS_REGION : region;

                            return getS3Object(targetBucket, targetRegion, key);
                        })());
                }

                if (isAcceptableForCatching) {
                    const imageData = await streamToBuffer(s3Object.createReadStream());

                    await redisClient.setEx(
                        imageKey,
                        Number(process.env.ARTIFACT_THUMBNAILS_CACHE_LIFETIME || 86400),
                        JSON.stringify({
                            filename: key.split("/").pop(),
                            ContentLength: imageData.byteLength,
                            Content: Buffer.from(imageData).toString("hex"),
                        }),
                    );

                    s3Object.createReadStream = () => Readable.from(Buffer.from(imageData));
                }

                responseWithImage(res, s3Object, key.split("/").pop());
            } catch (err) {
                console.error("Error fetching from S3:", err);
                if (err.code === "NoSuchKey") {
                    res.status(404).send("File not found.");
                } else {
                    res.status(500).send("Error retrieving file.");
                }
            }
        } else {
            validateError(new Error("No object is found"), res);
        }
    },
);

router.get(
    "/hoursAggregatedCount",
    assignEndpointId.bind(this, endpointIds.FETCH_HOURS_AGGREGATED_COUNT),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("startTimestamp").isInt().toInt().withMessage("Invalid startTimestamp"),
                query("endTimestamp").isInt().toInt().withMessage("Invalid endTimestamp"),
                query("interval").isInt({ min: 1 }).toInt().withMessage("Interval must be a positive integer"),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        try {
            const { startTimestamp, endTimestamp, interval } = req.query;

            const startDate = new Date(Number(startTimestamp));
            const endDate = new Date(Number(endTimestamp));

            const aggregatedData = await db.qmai
                .collection("analysis_results")
                .aggregate([
                    { $match: { timestamp: { $gte: startDate, $lt: endDate } } },
                    {
                        $group: {
                            _id: {
                                $dateTrunc: {
                                    date: "$timestamp",
                                    unit: "minute",
                                    binSize: interval,
                                },
                            },
                            count: { $sum: 1 },
                        },
                    },
                    {
                        $project: {
                            timestamp: "$_id",
                            count: 1,
                            _id: 0,
                        },
                    },
                    { $sort: { timestamp: 1 } },
                ])
                .toArray();

            const statistics = aggregatedData.reduce((acc, entry) => {
                acc[entry.timestamp.toISOString()] = entry.count;
                return acc;
            }, {});

            const concatenatedTimeSeries = {
                ...generateTimeSeries(Number(startTimestamp), Number(endTimestamp), interval),
                ...statistics,
            };
            const convertedToArray = Object.entries(concatenatedTimeSeries)
                .map(([key, value]) => ({
                    timestamp: key,
                    count: value,
                }))
                .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
            res.status(200).json(convertedToArray);
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Artifacts
 *   description: Fetch vessel artifacts data
 * components:
 *   schemas:
 *     Artifact:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Document id of the artifact.
 *           example: "66ee885da6db303e08c075cb"
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the artifact was recorded.
 *           example: "2024-09-21T08:41:43.736Z"
 *         bucket_name:
 *           type: string
 *           description: Name of the S3 bucket where the artifact is stored.
 *           example: "smartmast-prototype-33-ap"
 *         unit_id:
 *           type: string
 *           description: Identifier for the vessel.
 *           example: "prototype-33"
 *         image_path:
 *           type: string
 *           description: Path to the artifact image in the S3 bucket.
 *           example: "artifacts/2024-09-21T07:22:34.941Z/image/prototype-33_cam-1_2024-09-21T08:41:43.736Z.jpg"
 *         location:
 *           type: object
 *           properties:
 *             latitude:
 *               type: number
 *               description: Latitude coordinate of the artifact.
 *               example: 9.734783799999999
 *             longitude:
 *               type: number
 *               description: Longitude coordinate of the artifact.
 *               example: 118.7199356
 *         category:
 *           type: string
 *           description: Category of the object in the artifact.
 *           example: "Banca"
 *         super_category:
 *           type: string
 *           description: Broad classification of the object.
 *           example: "Fishing"
 *         color:
 *           type: string
 *           description: Color description of the object in the artifact.
 *           example: "Gray and white"
 *         size:
 *           type: string
 *           description: Size of the object.
 *           example: "Small"
 *         others:
 *           type: string
 *           description: Additional information or description of the object.
 *           example: "Traditional outrigger design, likely used for small-scale fishing."
 *         aws_region:
 *           type: string
 *           description: AWS region where the artifact is stored.
 *           example: "ap-southeast-1"
 */

/**
 * @swagger
 * /artifacts/filters:
 *   get:
 *     summary: Fetch available artifact filters
 *     description: Retrieves distinct values for artifact filter fields, such as categories, country flags, onboard vessel names, and super categories.
 *     tags: [Artifacts]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: A list of available filter options.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 categories:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of distinct artifact categories.
 *                   example: ["Aircraft Carrier", "Bamboo Raft", "Banca"]
 *                 countryFlags:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of distinct country flags.
 *                   example: ["Alaska", "Argentina", "Australia"]
 *                 onboardVesselNames:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of distinct onboard vessel names.
 *                   example: ["BRP Bagacay (MRRV-4410)", "BRP Cabra (MRRV-4409)"]
 *                 superCategories:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of distinct super categories.
 *                   example: ["Cargo", "Fishing"]
 *       401:
 *         description: Unauthorized. User must be authenticated.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Authentication error message.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */

/**
 * @swagger
 * /artifacts/{vesselName}:
 *   post:
 *     summary: Fetch artifacts by vessel
 *     description: Retrieves a list of artifacts for a specific vessel within a given time range. Supports filtering by time range, exclusion of specific IDs, and the option to include artifacts with null super categories.
 *     tags: [Artifacts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: vesselName
 *         required: true
 *         schema:
 *           type: string
 *         description: The name or ID of the vessel to fetch artifacts for.
 *         example: "prototype-33"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               startTimestamp:
 *                 required: false
 *                 type: integer
 *                 description: The start unix timestamp in milliseconds for filtering artifacts.
 *                 example: 1726876800000
 *               endTimestamp:
 *                 required: false
 *                 type: integer
 *                 description: The end unix timestamp in milliseconds for filtering artifacts.
 *                 example: 1726963200000
 *               excludeIds:
 *                 required: false
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: A list of artifact IDs to exclude from the results.
 *                 example: ["66ef4f6ea6db303e08c0767c", "66ef4f95a6db303e08c0767d"]
 *     responses:
 *       200:
 *         description: A list of artifacts for the specified vessel.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Artifact'
 *       400:
 *         description: Invalid request parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message describing the invalid request.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       403:
 *         description: Cannot access this unit
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Cannot access this unit.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */

/**
 * @swagger
 * /artifacts:
 *   post:
 *     summary: Fetch paginated artifacts
 *     description: Retrieves a paginated list of artifacts from the database based on applied filters. Supports filtering by time range, category, and exclusion of specific IDs.
 *     tags: [Artifacts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               excludeIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: A list of artifact IDs to exclude from the results.
 *                 example: ["66ef4f6ea6db303e08c0767c", "66ef4f95a6db303e08c0767d"]
 *               page:
 *                 type: integer
 *                 description: The page number for pagination (must be a positive integer).
 *                 example: 1
 *               pageSize:
 *                 type: integer
 *                 description: The number of results per page (must be a positive integer).
 *                 example: 20
 *               filters:
 *                 type: object
 *                 description: Additional filters to apply to the query.
 *                 properties:
 *                   startTime:
 *                     type: string
 *                     format: date-time
 *                     description: Start timestamp for filtering artifacts.
 *                     example: "2024-09-21T08:41:43.736Z"
 *                   endTime:
 *                     type: string
 *                     format: date-time
 *                     description: End timestamp for filtering artifacts.
 *                     example: "2024-09-22T08:41:43.736Z"
 *                   category:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of categories to filter artifacts.
 *                     example: ["Banca", "Trawler"]
 *                   color:
 *                     type: array
 *                     items:
 *                       type: string
 *                       description: List of colors to filter artifacts.
 *                       example: ["Red", "Blue"]
 *                   size:
 *                     type: array
 *                     items:
 *                       type: string
 *                       description: List of sizes to filter artifacts.
 *                       example: ["Small", "Large"]
 *                   weapon:
 *                     type: array
 *                     items:
 *                       type: string
 *                       description: List of weapons to filter artifacts.
 *                       example: ["Missile", "Gun"]
 *
 *     responses:
 *       200:
 *         description: A list of paginated artifacts.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 artifacts:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Artifact'
 *                 page:
 *                   type: integer
 *                   description: Current page number.
 *                   example: 1
 *                 pageSize:
 *                   type: integer
 *                   description: Number of results per page.
 *                   example: 20
 *                 totalCount:
 *                   type: integer
 *                   description: Total number of artifacts matching the query.
 *                   example: 100
 *       400:
 *         description: Invalid request parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message describing the invalid request.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       403:
 *         description: Cannot access this unit
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Cannot access this unit.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */

/**
 * @swagger
 * /artifacts/hoursAggregatedCount:
 *   get:
 *     summary: Fetch aggregated artifact counts
 *     description: Retrieves aggregated artifact counts based on a specified interval in minutes within a given time range.
 *     tags: [Artifacts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startTimestamp
 *         required: true
 *         schema:
 *           type: integer
 *           description: Start timestamp in milliseconds.
 *           example: 1726876800000
 *       - in: query
 *         name: endTimestamp
 *         required: true
 *         schema:
 *           type: integer
 *           description: End timestamp in milliseconds.
 *           example: 1726963200000
 *       - in: query
 *         name: interval
 *         required: true
 *         schema:
 *           type: integer
 *           description: Interval in minutes for aggregation (must be a positive integer).
 *           example: 60
 *     responses:
 *       200:
 *         description: Aggregated artifact counts.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   timestamp:
 *                     type: string
 *                     format: date-time
 *                     description: Aggregated timestamp.
 *                     example: "2025-03-15T16:00:00.000Z"
 *                   count:
 *                     type: integer
 *                     description: Count of artifacts for the given timestamp.
 *                     example: 100
 *       400:
 *         description: Invalid request parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message describing the invalid request.
 *       401:
 *         description: Unauthorized. User must be authenticated.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Authentication error message.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */
