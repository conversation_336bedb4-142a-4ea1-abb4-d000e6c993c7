import { useState, useEffect } from "react";
import { Modal, TextField, Button, Grid, FormControlLabel, Switch, MenuItem } from "@mui/material";
import ModalContainer from "../../../components/ModalContainer";
import S3ImageUpload from "../../../components/S3ImageUpload";
import { useToaster } from "../../../hooks/ToasterHook";
import theme from "../../../theme";

const EditVesselModal = ({ open, onClose, vessel, onSubmit, units = [], unitsLoading = false, existingVessels = [] }) => {
    const toaster = useToaster();
    const [formData, setFormData] = useState({
        name: "",
        thumbnail_file: null,
        unit_id: "",
        is_active: true,
    });
    const [errors, setErrors] = useState({});
    const [loading, setLoading] = useState(false);

    const assignedUnitIds = existingVessels.map((vessel) => vessel.unit_id);

    useEffect(() => {
        if (vessel) {
            setFormData({
                name: vessel.name || "",
                thumbnail_file: vessel.thumbnail_s3_key || null,
                unit_id: vessel.unit_id || "",
                is_active: vessel.is_active !== undefined ? vessel.is_active : true,
            });
        }
    }, [vessel]);

    const handleChange = (field) => (event) => {
        const value = field === "is_active" ? event.target.checked : event.target.value;
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));

        if (errors[field]) {
            setErrors((prev) => ({
                ...prev,
                [field]: "",
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = "Vessel name is required";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async () => {
        if (!validateForm()) {
            return;
        }

        setLoading(true);

        try {
            const submitData = new FormData();
            let hasChanges = false;

            if (formData.name !== vessel.name) {
                submitData.append("name", formData.name);
                hasChanges = true;
            }
            if (formData.unit_id !== vessel.unit_id) {
                submitData.append("unit_id", formData.unit_id);
                hasChanges = true;
            }
            if (formData.is_active !== vessel.is_active) {
                submitData.append("is_active", formData.is_active);
                hasChanges = true;
            }
            if (formData.thumbnail_file instanceof File) {
                submitData.append("thumbnail_file", formData.thumbnail_file);
                hasChanges = true;
            }

            if (!hasChanges) {
                handleClose();
                return;
            }

            const result = await onSubmit(submitData);

            if (result.success) {
                handleClose();
            } else {
                toaster(result.error || "Failed to update vessel", { variant: "error" });
            }
        } catch {
            toaster("An unexpected error occurred", { variant: "error" });
        } finally {
            setLoading(false);
        }
    };

    const handleClose = () => {
        setErrors({});
        onClose();
    };

    if (!vessel) return null;

    return (
        <Modal open={open} onClose={handleClose}>
            <ModalContainer title={"Edit Vessel"} onClose={handleClose}>
                <Grid container direction="column" sx={{ gap: 2 }}>
                    <Grid>
                        <TextField
                            value={formData.name}
                            sx={{ minWidth: { xs: 250, sm: 500 } }}
                            onChange={handleChange("name")}
                            label="Vessel Name"
                            variant="filled"
                            required
                            error={!!errors.name}
                        />
                    </Grid>

                    <Grid>
                        <TextField
                            select
                            value={formData.unit_id}
                            sx={{ minWidth: { xs: 250, sm: 500 } }}
                            onChange={handleChange("unit_id")}
                            label="Unit ID (Optional)"
                            variant="filled"
                            disabled={unitsLoading}
                            error={!!errors.unit_id}
                            helperText={unitsLoading ? "Loading units..." : errors.unit_id}
                        >
                            <MenuItem value={""}>
                                <em>No Unit Assigned</em>
                            </MenuItem>
                            {units.length === 0 && !unitsLoading ? (
                                <MenuItem disabled>No units available</MenuItem>
                            ) : (
                                units.map((unit) => (
                                    <MenuItem key={unit.unit_id} value={unit.unit_id} disabled={assignedUnitIds.includes(unit.unit_id)}>
                                        {unit.unit_id}
                                    </MenuItem>
                                ))
                            )}
                        </TextField>
                    </Grid>

                    <Grid>
                        <S3ImageUpload
                            value={formData.thumbnail_file}
                            onChange={(fileData) => setFormData((prev) => ({ ...prev, thumbnail_file: fileData }))}
                            label="Vessel Thumbnail (optional)"
                            maxSizeBytes={1 * 1024 * 1024} // 1MB
                            error={!!errors.thumbnail_file}
                            helperText={errors.thumbnail_file || "Upload an image for the vessel thumbnail"}
                        />
                    </Grid>

                    <Grid>
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={formData.is_active}
                                    onChange={handleChange("is_active")}
                                    sx={{
                                        height: "50px",
                                        width: "80px",
                                        borderRadius: "50px",
                                        "& .MuiSwitch-switchBase": {
                                            padding: "15px 4px",
                                            transform: "translate(9px, -2px)",
                                        },
                                        "& .MuiSwitch-track": {
                                            backgroundColor: "#FFFFFF",
                                            height: "30px",
                                            borderRadius: "50px",
                                        },
                                        "& .Mui-checked+.MuiSwitch-track": {
                                            backgroundColor: theme.palette.custom.mainBlue + " !important",
                                            opacity: "1 !important",
                                        },
                                        "& .Mui-checked.MuiSwitch-switchBase": {
                                            transform: "translate(36px, -2px)",
                                        },
                                        "& .MuiSwitch-thumb": {
                                            backgroundColor: "#FFFFFF",
                                            height: "28px",
                                            width: "28px",
                                        },
                                        "& .Mui-disabled": {
                                            opacity: 0.4,
                                        },
                                        "& .Mui-disabled+.MuiSwitch-track": {
                                            opacity: "0.3 !important",
                                        },
                                    }}
                                />
                            }
                            label={formData.is_active ? "Active Vessel" : "Inactive Vessel"}
                        />
                    </Grid>

                    <Grid sx={{ justifyContent: "center", display: "flex", gap: 1 }}>
                        <Button
                            variant="contained"
                            onClick={handleSubmit}
                            disabled={loading || !formData.name || !!Object.keys(errors).find((key) => errors[key])}
                        >
                            {loading ? "Updating..." : "Update Vessel"}
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default EditVesselModal;
